import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export class OriginalWebUIProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'agent-zero-chat';
    private _view?: vscode.WebviewView;

    constructor(private readonly context: vscode.ExtensionContext) { }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('OriginalWebUIProvider: resolveWebviewView called');
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.context.extensionPath, 'media')),
                this.context.extensionUri
            ]
        };

        console.log('OriginalWebUIProvider: Setting webview HTML');
        console.log('OriginalWebUIProvider: Extension path:', this.context.extensionPath);
        console.log('OriginalWebUIProvider: WebUI path:', path.join(this.context.extensionPath, '..', 'webui'));

        webviewView.webview.html = this.getOriginalWebUI(webviewView.webview);
        console.log('OriginalWebUIProvider: HTML set successfully');
    }

    public show(): void {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    private getOriginalWebUI(webview: vscode.Webview): string {
        try {
            // Path to the media folder (where webui files are copied)
            const webuiPath = path.join(this.context.extensionPath, 'media');
            const indexPath = path.join(webuiPath, 'index.html');

            console.log('OriginalWebUIProvider: Checking media path:', webuiPath);
            console.log('OriginalWebUIProvider: Index path:', indexPath);
            console.log('OriginalWebUIProvider: Index exists:', fs.existsSync(indexPath));

            // Check if media files exist
            if (!fs.existsSync(indexPath)) {
                console.error('OriginalWebUIProvider: WebUI index.html not found in media folder');
                return this.getErrorHtml('WebUI files not found in media folder. Please run "npm run copy-assets" to copy webui files.');
            }

            // Read the original index.html
            let html = fs.readFileSync(indexPath, 'utf8');
            console.log('OriginalWebUIProvider: HTML file read, length:', html.length);

            // Convert relative paths to webview URIs
            html = this.convertPathsToWebviewUris(html, webview, webuiPath);
            console.log('OriginalWebUIProvider: Paths converted, final HTML length:', html.length);

            return html;
        } catch (error) {
            console.error('OriginalWebUIProvider: Error loading original webui:', error);
            return this.getErrorHtml(`Error loading webui: ${error}`);
        }
    }

    private getTestHtml(): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Agent Zero Test</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-foreground);
                        padding: 20px;
                        margin: 0;
                    }
                    .container {
                        text-align: center;
                        padding: 40px 20px;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 20px;
                        color: var(--vscode-textLink-foreground);
                    }
                    .message {
                        font-size: 16px;
                        line-height: 1.5;
                        margin-bottom: 20px;
                    }
                    .success {
                        color: var(--vscode-terminal-ansiGreen);
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="title">🤖 Agent Zero WebUI</div>
                    <div class="message success">✅ Webview is working!</div>
                    <div class="message">This is a test to verify that the webview provider is functioning correctly.</div>
                    <div class="message">If you can see this, the sidebar webview is properly configured.</div>
                </div>
            </body>
            </html>
        `;
    }

    private convertPathsToWebviewUris(html: string, webview: vscode.Webview, webuiPath: string): string {
        // Convert CSS links
        html = html.replace(/href="([^"]+\.css)"/g, (match, cssPath) => {
            if (!cssPath.startsWith('http')) {
                const fullPath = path.join(webuiPath, cssPath);
                if (fs.existsSync(fullPath)) {
                    const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                    return `href="${uri}"`;
                }
            }
            return match;
        });

        // Convert JS script sources
        html = html.replace(/src="([^"]+\.js)"/g, (match, jsPath) => {
            if (!jsPath.startsWith('http')) {
                const fullPath = path.join(webuiPath, jsPath);
                if (fs.existsSync(fullPath)) {
                    const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                    return `src="${uri}"`;
                }
            }
            return match;
        });

        // Convert image sources and favicons
        html = html.replace(/(src|href)="([^"]+\.(png|jpg|jpeg|gif|svg|ico))"/g, (match, attr, imgPath) => {
            if (!imgPath.startsWith('http')) {
                const fullPath = path.join(webuiPath, imgPath);
                if (fs.existsSync(fullPath)) {
                    const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                    return `${attr}="${uri}"`;
                }
            }
            return match;
        });

        // Convert vendor files (flatpickr, alpine, etc.)
        html = html.replace(/(src|href)="(vendor\/[^"]+)"/g, (match, attr, vendorPath) => {
            const fullPath = path.join(webuiPath, vendorPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        // Convert public folder assets
        html = html.replace(/(src|href)="(public\/[^"]+)"/g, (match, attr, publicPath) => {
            const fullPath = path.join(webuiPath, publicPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        // Convert components folder
        html = html.replace(/(src|href)="(components\/[^"]+)"/g, (match, attr, componentPath) => {
            const fullPath = path.join(webuiPath, componentPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        // Convert js folder files
        html = html.replace(/(src|href)="(js\/[^"]+)"/g, (match, attr, jsPath) => {
            const fullPath = path.join(webuiPath, jsPath);
            if (fs.existsSync(fullPath)) {
                const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                return `${attr}="${uri}"`;
            }
            return match;
        });

        return html;
    }

    private getErrorHtml(errorMessage: string): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Agent Zero WebUI Error</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-foreground);
                        padding: 20px;
                        margin: 0;
                    }
                    .error-container {
                        text-align: center;
                        padding: 40px 20px;
                    }
                    .error-icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                    .error-title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                        color: var(--vscode-errorForeground);
                    }
                    .error-message {
                        font-size: 16px;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        opacity: 0.8;
                    }
                    .error-help {
                        font-size: 14px;
                        opacity: 0.6;
                        border-top: 1px solid var(--vscode-panel-border);
                        padding-top: 20px;
                        margin-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">WebUI Loading Error</div>
                    <div class="error-message">${errorMessage}</div>
                    <div class="error-help">
                        <p>Make sure to run the copy assets script to copy webui files to the media folder.</p>
                        <p>Run this command in the vscode-extension directory:</p>
                        <pre>npm run copy-assets</pre>
                        <p>Expected structure:</p>
                        <pre>
vscode-extension/
├── media/
│   ├── index.html
│   ├── index.js
│   └── ...
└── src/
    └── ...
                        </pre>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
}
