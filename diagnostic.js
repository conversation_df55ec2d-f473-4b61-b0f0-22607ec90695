// ملف تشخيص لفحص حالة Extension
const fs = require('fs');
const path = require('path');

console.log('🔍 فحص حالة Agent Zero VS Code Extension...\n');

// فحص الملفات الأساسية
const requiredFiles = [
    'package.json',
    'src/extension.ts',
    'src/providers/OriginalWebUIProvider.ts',
    'media/index.html',
    'media/index.css',
    'media/index.js'
];

console.log('📁 فحص الملفات الأساسية:');
requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${file}`);
    
    if (exists && file.endsWith('.html')) {
        const size = fs.statSync(file).size;
        console.log(`   📏 الحجم: ${size} بايت`);
    }
});

// فحص مجلدات media
console.log('\n📂 فحص مجلدات media:');
const mediaFolders = ['css', 'js', 'public', 'vendor', 'components'];
mediaFolders.forEach(folder => {
    const folderPath = path.join('media', folder);
    const exists = fs.existsSync(folderPath);
    const status = exists ? '✅' : '❌';
    console.log(`${status} media/${folder}/`);
    
    if (exists) {
        const files = fs.readdirSync(folderPath);
        console.log(`   📄 عدد الملفات: ${files.length}`);
    }
});

// فحص package.json
console.log('\n📦 فحص package.json:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log(`✅ اسم Extension: ${packageJson.name}`);
    console.log(`✅ الإصدار: ${packageJson.version}`);
    console.log(`✅ Publisher: ${packageJson.publisher}`);
    
    // فحص الأوامر
    const commands = packageJson.contributes?.commands || [];
    console.log(`✅ عدد الأوامر: ${commands.length}`);
    
    // فحص Views
    const views = packageJson.contributes?.views?.['agent-zero'] || [];
    console.log(`✅ عدد Views: ${views.length}`);
    
    // فحص Scripts
    const scripts = packageJson.scripts || {};
    const hasDevScript = 'dev' in scripts;
    const hasCopyAssets = 'copy-assets' in scripts;
    console.log(`${hasDevScript ? '✅' : '❌'} dev script`);
    console.log(`${hasCopyAssets ? '✅' : '❌'} copy-assets script`);
    
} catch (error) {
    console.log('❌ خطأ في قراءة package.json:', error.message);
}

// فحص out folder
console.log('\n🔨 فحص مجلد out (الملفات المجمعة):');
const outExists = fs.existsSync('out');
console.log(`${outExists ? '✅' : '❌'} مجلد out موجود`);

if (outExists) {
    const extensionJs = fs.existsSync('out/extension.js');
    console.log(`${extensionJs ? '✅' : '❌'} out/extension.js`);
    
    if (extensionJs) {
        const size = fs.statSync('out/extension.js').size;
        console.log(`   📏 حجم extension.js: ${Math.round(size/1024)} KB`);
    }
}

// فحص webui الأصلي
console.log('\n🌐 فحص WebUI الأصلي:');
const webuiPath = path.join('..', 'webui');
const webuiExists = fs.existsSync(webuiPath);
console.log(`${webuiExists ? '✅' : '❌'} مجلد ../webui موجود`);

if (webuiExists) {
    const indexExists = fs.existsSync(path.join(webuiPath, 'index.html'));
    console.log(`${indexExists ? '✅' : '❌'} ../webui/index.html`);
}

// نصائح للإصلاح
console.log('\n💡 نصائح للإصلاح:');
console.log('1. إذا كانت ملفات media مفقودة، شغل: npm run copy-assets');
console.log('2. إذا كان مجلد out مفقود، شغل: npm run compile');
console.log('3. إذا كان Extension لا يعمل، شغل: npm run dev');
console.log('4. لاختبار Extension، شغل: npm run test-extension أو اضغط F5 في VS Code');

// معلومات إضافية
console.log('\n📋 معلومات إضافية:');
console.log('- مجلد العمل الحالي:', process.cwd());
console.log('- نظام التشغيل:', process.platform);
console.log('- إصدار Node.js:', process.version);

console.log('\n🎉 انتهى الفحص!');
console.log('📖 راجع README-AR.md للحصول على تعليمات مفصلة');
