# Agent Zero VS Code Extension 🤖

## نظرة عامة
هذا Extension يدمج واجهة Agent Zero WebUI مباشرة في VS Code، مما يتيح لك استخدام قوة الذكاء الاصطناعي أثناء البرمجة.

## المشكلة التي تم حلها ✅
كانت المشكلة أن السايد بار لا يظهر شاشة الدردشة الخاصة بـ Agent Zero. تم حل هذه المشكلة عبر:

1. **تصحيح مسارات الملفات**: تم تغيير المسار من `../webui` إلى `media/`
2. **نسخ ملفات WebUI**: تم إنشاء سكريبت لنسخ ملفات الواجهة إلى مجلد `media`
3. **تحديث إعدادات Webview**: تم تحديث `localResourceRoots` لتشير إلى المجلد الصحيح

## كيفية الاستخدام 🚀

### 1. إعد<PERSON> البيئة
```bash
cd vscode-extension
npm install
npm run dev
```

### 2. تشغيل Extension في وضع التطوير
```bash
# الطريقة الأولى: من VS Code
# اضغط F5 في VS Code

# الطريقة الثانية: من Terminal
npm run test-extension
```

### 3. استخدام السايد بار
1. في النافذة الجديدة، ابحث عن أيقونة Agent Zero في Activity Bar (الشريط الجانبي)
2. انقر على الأيقونة لفتح السايد بار
3. ستظهر واجهة Agent Zero WebUI في السايد بار
4. يمكنك الآن استخدام الدردشة والوظائف المختلفة

## الوظائف المتاحة 🛠️

### السايد بار
- **Agent Zero WebUI**: الواجهة الأصلية للدردشة
- **Advanced Chat**: واجهة دردشة متقدمة
- **Active Agents**: إدارة الوكلاء النشطين
- **Memory & Knowledge**: إدارة الذاكرة والمعرفة
- **Tools**: أدوات مختلفة
- **Enhanced Memory**: ذاكرة محسنة
- **Multi-Agent System**: نظام متعدد الوكلاء

### الأوامر المتاحة
- `Ctrl+Shift+A`: فتح الدردشة
- `Ctrl+Shift+W`: فتح WebUI
- `Ctrl+Shift+N`: إنشاء وكيل جديد
- `Ctrl+Shift+E`: تنفيذ الكود
- `Ctrl+Shift+F`: البحث في المعرفة

## هيكل المشروع 📁
```
vscode-extension/
├── src/
│   ├── extension.ts              # نقطة البداية
│   ├── providers/
│   │   ├── OriginalWebUIProvider.ts  # مزود واجهة WebUI
│   │   └── ...
│   └── ...
├── media/                        # ملفات WebUI المنسوخة
│   ├── index.html
│   ├── index.css
│   ├── index.js
│   ├── css/
│   ├── js/
│   └── ...
├── package.json                  # إعدادات Extension
└── copy-webui-assets.js         # سكريبت نسخ الملفات
```

## استكشاف الأخطاء 🔧

### المشكلة: السايد بار لا يظهر الواجهة
**الحل:**
1. تأكد من تشغيل `npm run copy-assets`
2. تأكد من وجود ملفات في مجلد `media/`
3. أعد تجميع Extension بـ `npm run compile`

### المشكلة: الملفات لا تحمل بشكل صحيح
**الحل:**
1. تحقق من مسارات الملفات في `OriginalWebUIProvider.ts`
2. تأكد من أن `localResourceRoots` يشير إلى `media/`

### المشكلة: Extension لا يعمل
**الحل:**
1. تحقق من وجود أخطاء في Developer Console
2. تأكد من تفعيل Extension في VS Code
3. أعد تشغيل VS Code

## التطوير 👨‍💻

### إضافة وظائف جديدة
1. عدل الملفات في مجلد `src/`
2. شغل `npm run dev` لنسخ الملفات وتجميع الكود
3. اختبر في VS Code بـ F5

### تحديث واجهة WebUI
1. عدل الملفات في مجلد `../webui/`
2. شغل `npm run copy-assets` لنسخ التحديثات
3. أعد تجميع بـ `npm run compile`

## الدعم والمساعدة 💬
إذا واجهت أي مشاكل:
1. تحقق من ملف `test-interface.html` للحصول على معلومات الحالة
2. راجع Developer Console في VS Code للأخطاء
3. تأكد من أن جميع الملفات موجودة في المجلدات الصحيحة

## الخطوات التالية 🎯
- [ ] اختبار جميع الوظائف في السايد بار
- [ ] إضافة المزيد من الأوامر والاختصارات
- [ ] تحسين أداء تحميل الملفات
- [ ] إضافة دعم للغات أخرى
