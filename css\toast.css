#toast {
  position: relative;
  width: 100%;
  background-color: #333;
  font-family: "Rubik", Arial, Helvetica, sans-serif;
  color: #fff;
  padding: 0.6rem 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: none;
  align-items: center;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.19, 0.86, 0.47, 1), background-color 0.3s ease;
  will-change: transform;
}

.toast__content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  margin-left: var(--spacing-xs);
  margin-right: 16px;
}

.toast__title {
  font-size: 0.6rem;
  color: #fff;
  opacity: 0.7;
  margin-bottom: 0.3rem;
}

.toast__separator {
  height: 1px;
  background: rgba(255, 255, 255, 0.15);
  margin-bottom: 0.3rem;
}

.toast__message {
  margin: 0;
  /* max-width: 320px; */
  text-overflow: ellipsis !important;
}

#toast.show {
  display: flex;
  transform: translateY(0);
}

#toast.hide {
  transform: translateY(100%);
}

#toast.toast--success {
  background-color: #4CAF50;
}

#toast.toast--error {
  background-color: #731811;
}

#toast.toast--info {
  background-color: #2196F3;
}

.toast__close,
.toast__copy {
  background-color: transparent;
  border: none;
  font-family: "Rubik", Arial, Helvetica, sans-serif;
  color: #fff;
  cursor: pointer;
  font-size: 16px;
  margin-left: 8px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.toast__close:hover,
.toast__copy:hover {
  opacity: 1;
}

/* Animations */

@keyframes toastIn {
  from {
      transform: translateY(100%);
  }
  to {
      transform: translateY(0);
  }
}

@keyframes toastOut {
  from {
      transform: translateY(0);
  }
  to {
      transform: translateY(100%);
  }
}